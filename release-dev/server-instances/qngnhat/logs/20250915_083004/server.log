2025-09-15T08:30:05.462+07:00  INFO 8950 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 8950 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-15T08:30:05.463+07:00  INFO 8950 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-15T08:30:06.797+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:06.920+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 116 ms. Found 22 JPA repository interfaces.
2025-09-15T08:30:06.937+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:06.940+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-09-15T08:30:06.941+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:06.952+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 10 JPA repository interfaces.
2025-09-15T08:30:06.953+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.036+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 82 ms. Found 3 JPA repository interfaces.
2025-09-15T08:30:07.074+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.083+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 1 JPA repository interface.
2025-09-15T08:30:07.106+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.110+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 2 JPA repository interfaces.
2025-09-15T08:30:07.110+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.117+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 6 JPA repository interfaces.
2025-09-15T08:30:07.122+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.132+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 5 JPA repository interfaces.
2025-09-15T08:30:07.136+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.142+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 3 JPA repository interfaces.
2025-09-15T08:30:07.142+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.143+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-15T08:30:07.143+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.153+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 10 JPA repository interfaces.
2025-09-15T08:30:07.160+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.164+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-15T08:30:07.168+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.173+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-09-15T08:30:07.174+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.185+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 12 JPA repository interfaces.
2025-09-15T08:30:07.185+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.191+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 4 JPA repository interfaces.
2025-09-15T08:30:07.192+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.192+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-15T08:30:07.192+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.193+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-15T08:30:07.194+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.203+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 7 JPA repository interfaces.
2025-09-15T08:30:07.204+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.205+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-15T08:30:07.206+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.206+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-15T08:30:07.206+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.226+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 19 JPA repository interfaces.
2025-09-15T08:30:07.245+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.255+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 8 JPA repository interfaces.
2025-09-15T08:30:07.256+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.261+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 5 JPA repository interfaces.
2025-09-15T08:30:07.262+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.271+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 7 JPA repository interfaces.
2025-09-15T08:30:07.272+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.280+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 9 JPA repository interfaces.
2025-09-15T08:30:07.280+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.285+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-15T08:30:07.286+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.297+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 12 JPA repository interfaces.
2025-09-15T08:30:07.297+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.310+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 14 JPA repository interfaces.
2025-09-15T08:30:07.310+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.331+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 24 JPA repository interfaces.
2025-09-15T08:30:07.331+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.333+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-15T08:30:07.339+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.341+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 0 JPA repository interfaces.
2025-09-15T08:30:07.341+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.354+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 12 JPA repository interfaces.
2025-09-15T08:30:07.356+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.414+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 57 ms. Found 66 JPA repository interfaces.
2025-09-15T08:30:07.414+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.416+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-15T08:30:07.422+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-15T08:30:07.427+07:00  INFO 8950 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-09-15T08:30:07.755+07:00  INFO 8950 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-15T08:30:07.760+07:00  INFO 8950 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-15T08:30:08.186+07:00  WARN 8950 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-15T08:30:08.462+07:00  INFO 8950 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-15T08:30:08.465+07:00  INFO 8950 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-15T08:30:08.480+07:00  INFO 8950 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-15T08:30:08.480+07:00  INFO 8950 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2824 ms
2025-09-15T08:30:08.542+07:00  WARN 8950 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-15T08:30:08.542+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-15T08:30:08.753+07:00  INFO 8950 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3ef97efc
2025-09-15T08:30:08.756+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-15T08:30:08.764+07:00  WARN 8950 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-15T08:30:08.765+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-15T08:30:08.771+07:00  INFO 8950 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1847da64
2025-09-15T08:30:08.771+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-15T08:30:08.772+07:00  WARN 8950 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-15T08:30:08.772+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-15T08:30:08.800+07:00  INFO 8950 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@42748f59
2025-09-15T08:30:08.801+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-15T08:30:08.801+07:00  WARN 8950 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-15T08:30:08.801+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-15T08:30:08.817+07:00  INFO 8950 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@54d3b4fe
2025-09-15T08:30:08.817+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-15T08:30:08.817+07:00  WARN 8950 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-15T08:30:08.818+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-15T08:30:08.967+07:00  INFO 8950 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@da8853c
2025-09-15T08:30:08.967+07:00  INFO 8950 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-15T08:30:08.968+07:00  INFO 8950 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-15T08:30:09.049+07:00  INFO 8950 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-15T08:30:09.053+07:00  INFO 8950 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@69f01f4b{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15029335181537490227/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@68a06914{STARTED}}
2025-09-15T08:30:09.054+07:00  INFO 8950 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@69f01f4b{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15029335181537490227/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@68a06914{STARTED}}
2025-09-15T08:30:09.055+07:00  INFO 8950 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@3bc00359{STARTING}[12.0.15,sto=0] @4437ms
2025-09-15T08:30:09.206+07:00  INFO 8950 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-15T08:30:09.242+07:00  INFO 8950 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-15T08:30:09.264+07:00  INFO 8950 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-15T08:30:09.456+07:00  INFO 8950 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-15T08:30:09.545+07:00  WARN 8950 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-15T08:30:10.355+07:00  INFO 8950 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-15T08:30:10.365+07:00  INFO 8950 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4f724360] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-15T08:30:10.536+07:00  INFO 8950 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-15T08:30:10.778+07:00  INFO 8950 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-15T08:30:10.781+07:00  INFO 8950 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-15T08:30:10.793+07:00  INFO 8950 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-15T08:30:10.795+07:00  INFO 8950 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-15T08:30:10.825+07:00  INFO 8950 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-15T08:30:10.834+07:00  WARN 8950 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-15T08:30:13.278+07:00  INFO 8950 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-15T08:30:13.279+07:00  INFO 8950 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7cb4c98] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-15T08:30:13.567+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-15T08:30:13.567+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-15T08:30:13.574+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-15T08:30:13.574+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-15T08:30:13.589+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-15T08:30:13.589+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-15T08:30:14.140+07:00  INFO 8950 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-15T08:30:14.147+07:00  INFO 8950 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-15T08:30:14.149+07:00  INFO 8950 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-15T08:30:14.178+07:00  INFO 8950 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-15T08:30:14.189+07:00  WARN 8950 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-15T08:30:14.734+07:00  INFO 8950 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-15T08:30:14.735+07:00  INFO 8950 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3f37d844] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-15T08:30:14.790+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-15T08:30:14.790+07:00  WARN 8950 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-15T08:30:15.123+07:00  INFO 8950 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-15T08:30:15.156+07:00  INFO 8950 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-15T08:30:15.162+07:00  INFO 8950 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-15T08:30:15.162+07:00  INFO 8950 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-15T08:30:15.168+07:00  WARN 8950 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-15T08:30:15.298+07:00  INFO 8950 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-15T08:30:15.813+07:00  INFO 8950 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-15T08:30:15.818+07:00  INFO 8950 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-15T08:30:15.859+07:00  INFO 8950 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-15T08:30:15.921+07:00  INFO 8950 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-15T08:30:16.006+07:00  INFO 8950 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-15T08:30:16.033+07:00  INFO 8950 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-15T08:30:16.069+07:00  INFO 8950 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 10736105ms : this is harmless.
2025-09-15T08:30:16.078+07:00  INFO 8950 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-15T08:30:16.081+07:00  INFO 8950 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-15T08:30:16.093+07:00  INFO 8950 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 31802630ms : this is harmless.
2025-09-15T08:30:16.094+07:00  INFO 8950 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-15T08:30:16.106+07:00  INFO 8950 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-15T08:30:16.107+07:00  INFO 8950 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-15T08:30:18.132+07:00  INFO 8950 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-15T08:30:18.132+07:00  INFO 8950 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-15T08:30:18.132+07:00  WARN 8950 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-15T08:30:18.445+07:00  INFO 8950 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 15/09/2025@08:30:00+0700 to 15/09/2025@08:45:00+0700
2025-09-15T08:30:18.445+07:00  INFO 8950 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 15/09/2025@08:30:00+0700 to 15/09/2025@08:45:00+0700
2025-09-15T08:30:18.980+07:00  INFO 8950 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-15T08:30:18.980+07:00  INFO 8950 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-15T08:30:18.981+07:00  WARN 8950 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-15T08:30:19.259+07:00  INFO 8950 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-15T08:30:19.259+07:00  INFO 8950 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-15T08:30:19.259+07:00  INFO 8950 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-15T08:30:19.259+07:00  INFO 8950 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-15T08:30:19.259+07:00  INFO 8950 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-15T08:30:21.813+07:00  WARN 8950 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 8718d271-01f8-4335-9062-0d954896ffe9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-15T08:30:21.818+07:00  INFO 8950 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-15T08:30:22.196+07:00  INFO 8950 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-15T08:30:22.199+07:00  INFO 8950 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-15T08:30:22.199+07:00  INFO 8950 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-15T08:30:22.199+07:00  INFO 8950 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-15T08:30:22.273+07:00  INFO 8950 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-15T08:30:22.273+07:00  INFO 8950 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-15T08:30:22.275+07:00  INFO 8950 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-15T08:30:22.286+07:00  INFO 8950 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@b02edc8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-15T08:30:22.287+07:00  INFO 8950 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-15T08:30:22.288+07:00  INFO 8950 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-15T08:30:22.326+07:00  INFO 8950 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-15T08:30:22.326+07:00  INFO 8950 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-15T08:30:22.332+07:00  INFO 8950 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 17.247 seconds (process running for 17.714)
2025-09-15T08:31:04.280+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:31:25.322+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:31:25.327+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:32:06.377+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:33:03.472+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:33:24.513+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:33:24.517+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:34:06.577+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:35:02.633+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:35:02.634+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-15T08:35:28.665+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:35:28.673+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:36:05.729+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:37:06.827+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:37:28.862+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:37:28.873+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:38:04.937+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:39:06.030+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:39:29.076+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:39:29.080+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:40:04.129+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-15T08:40:04.130+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:41:06.230+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:41:28.264+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:41:28.265+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:42:03.321+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:43:06.421+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:43:27.450+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:43:27.453+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:44:02.506+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:45:05.611+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-15T08:45:05.617+07:00  INFO 8950 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 15/09/2025@08:45:05+0700
2025-09-15T08:45:05.639+07:00  INFO 8950 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 15/09/2025@08:45:00+0700 to 15/09/2025@09:00:00+0700
2025-09-15T08:45:05.639+07:00  INFO 8950 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 15/09/2025@08:45:00+0700 to 15/09/2025@09:00:00+0700
2025-09-15T08:45:05.639+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-15T08:45:05.639+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:45:26.690+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:45:26.693+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:46:06.742+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:46:57.687+07:00  INFO 8950 --- [qtp179899276-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node08jnij67wy8uy1pszulju7gu6q1
2025-09-15T08:46:57.687+07:00  INFO 8950 --- [qtp179899276-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0oe755itej8ku1byyp2fyuqg3b0
2025-09-15T08:46:58.012+07:00 ERROR 8950 --- [qtp179899276-36] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node08jnij67wy8uy1pszulju7gu6q1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "*****************SF46QHSABP3RHHWHJB6L55DTMOEV3HOHWM2SMWPUHKCX3JRYDDJUV2O4PCHQJH7EL4CJD5B3W4V3UVATD3CGZC2JPJZHE7DHIHQACRN2GVPVH3W3SSKOGBGG6PINM4DX2CS3WFFWTOHQEGWYZC3HPIRPOLDRCDGZHLWCVUSRAU77UFT2QBBBJ3HUX55CRSOO5DRQL4DGL7LAJLB7MRJLT2C4EVOM7AECC5TB6LKIOVD4X72EX3W43EOTKJLHRX6GVXKC5QUDNTA3XKOTK2ZCX5JRNLDNEXDSEHLLUZXETTV5R4CTKIAOVLPXLCKQMLYCZ4HVVKQFBYSNFIT2U3YHP5FBFXPZ5GXN4MHNENVGTLVTDFADVVCDIRQW4IXHEDJC5HGW7QVR5SGJN4QXPPAWZN4YMIVTA7OOPSA====",
  "company" : "beehph",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-15T08:46:58.012+07:00 ERROR 8950 --- [qtp179899276-34] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0oe755itej8ku1byyp2fyuqg3b0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "*****************SF46QHSABP3RHHWHJB6L55DTMOEV3HOHWM2SMWPUHKCX3JRYDDJUV2O4PCHQJH7EL4CJD5B3W4V3UVATD3CGZC2JPJZHE7DHIHQACRN2GVPVH3W3SSKOGBGG6PINM4DX2CS3WFFWTOHQEGWYZC3HPIRPOLDRCDGZHLWCVUSRAU77UFT2QBBBJ3HUX55CRSOO5DRQL4DGL7LAJLB7MRJLT2C4EVOM7AECC5TB6LKIOVD4X72EX3W43EOTKJLHRX6GVXKC5QUDNTA3XKOTK2ZCX5JRNLDNEXDSEHLLUZXETTV5R4CTKIAOVLPXLCKQMLYCZ4HVVKQFBYSNFIT2U3YHP5FBFXPZ5GXN4MHNENVGTLVTDFADVVCDIRQW4IXHEDJC5HGW7QVR5SGJN4QXPPAWZN4YMIVTA7OOPSA====",
  "company" : "beehph",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-15T08:46:58.013+07:00 ERROR 8950 --- [qtp179899276-36] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 104 common frames omitted

2025-09-15T08:46:58.013+07:00 ERROR 8950 --- [qtp179899276-34] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.produce(AdaptiveExecutionStrategy.java:195)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 97 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 108 common frames omitted

2025-09-15T08:46:58.017+07:00  INFO 8950 --- [qtp179899276-34] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-15T08:46:58.017+07:00  INFO 8950 --- [qtp179899276-36] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-15T08:47:00.431+07:00  INFO 8950 --- [qtp179899276-40] n.d.module.session.ClientSessionManager  : Add a client session id = node08jnij67wy8uy1pszulju7gu6q1, token = 18b27bd5373d2570b10dbb2d3d571140
2025-09-15T08:47:00.514+07:00  INFO 8950 --- [qtp179899276-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-15T08:47:04.838+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:47:06.478+07:00  INFO 8950 --- [qtp179899276-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-15T08:47:06.505+07:00  INFO 8950 --- [qtp179899276-83] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-15T08:47:26.003+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-15T08:47:26.020+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:48:06.079+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:49:04.171+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:49:25.274+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-15T08:49:25.278+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:50:06.324+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-15T08:50:06.327+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:51:03.425+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:51:24.462+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:51:24.464+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:52:06.517+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:53:02.609+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:53:28.679+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-15T08:53:28.698+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:54:05.762+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:55:06.850+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-15T08:55:06.851+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:55:28.898+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-15T08:55:28.913+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:56:04.983+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:57:06.085+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:57:29.142+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 8
2025-09-15T08:57:29.168+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T08:58:04.232+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:59:06.337+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T08:59:21.379+07:00  INFO 8950 --- [qtp179899276-36] n.d.module.session.ClientSessionManager  : Add a client session id = node08jnij67wy8uy1pszulju7gu6q1, token = 18b27bd5373d2570b10dbb2d3d571140
2025-09-15T08:59:21.393+07:00  INFO 8950 --- [qtp179899276-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-15T08:59:21.394+07:00  INFO 8950 --- [qtp179899276-88] n.d.module.session.ClientSessionManager  : Add a client session id = node08jnij67wy8uy1pszulju7gu6q1, token = 18b27bd5373d2570b10dbb2d3d571140
2025-09-15T08:59:21.405+07:00  INFO 8950 --- [qtp179899276-88] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-15T08:59:28.389+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-15T08:59:28.397+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-15T09:00:03.468+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-15T09:00:03.471+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-15T09:00:03.475+07:00  INFO 8950 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 15/09/2025@09:00:03+0700
2025-09-15T09:00:03.501+07:00  INFO 8950 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 15/09/2025@09:00:00+0700 to 15/09/2025@09:15:00+0700
2025-09-15T09:00:03.501+07:00  INFO 8950 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 15/09/2025@09:00:00+0700 to 15/09/2025@09:15:00+0700
2025-09-15T09:00:03.501+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-15T09:00:03.502+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-15T09:00:03.502+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T09:00:09.009+07:00  INFO 8950 --- [Scheduler-927048048-1] n.d.m.session.AppHttpSessionListener     : The session node0oe755itej8ku1byyp2fyuqg3b0 is destroyed.
2025-09-15T09:01:06.602+07:00  INFO 8950 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-15T09:01:27.673+07:00  INFO 8950 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-15T09:01:27.677+07:00  INFO 8950 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
