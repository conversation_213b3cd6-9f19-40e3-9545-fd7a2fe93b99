package cloud.datatp.fforwarder.core.message.repository;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CRMMessageSystemRepository extends JpaRepository<CRMMessageSystem, Serializable> {

  @Query("SELECT m FROM CRMMessageSystem m " +
    "WHERE m.scheduledAt >= :sessionFrom " +
    "AND m.scheduledAt < :sessionTo " +
    "AND m.status = :status " +
    "ORDER BY m.scheduledAt ASC")
  List<CRMMessageSystem> findMessagesForSession(
    @Param("sessionFrom") Date sessionFrom,
    @Param("sessionTo") Date sessionTo,
    @Param("status") MessageStatus status
  );

  @Query("SELECT m FROM CRMMessageSystem m " +
    "WHERE m.referenceId = :referenceId " +
    "AND m.referenceType = :referenceType " +
    "AND m.pluginName = :pluginName " +
    "AND m.status = :status")
  List<CRMMessageSystem> findByReferenceAndPlugin(
    @Param("referenceId") Long referenceId,
    @Param("referenceType") String referenceType,
    @Param("pluginName") String pluginName,
    @Param("status") MessageStatus status
  );

}