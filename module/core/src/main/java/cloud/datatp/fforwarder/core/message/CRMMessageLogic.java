package cloud.datatp.fforwarder.core.message;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.message.repository.CRMMessageSystemRepository;
import jakarta.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.Executor;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Getter
public class CRMMessageLogic extends Executor {

  @Autowired
  private MessageQueueManager queueManager;

  @Autowired
  private CRMMessageSystemRepository messageSystemRepo;

  @Autowired(required = false)
  private List<MessageServicePlugin> plugins = new ArrayList<>();

  private final Map<String, MessageServicePlugin> pluginsByType = new ConcurrentHashMap<>();

  @Autowired(required = false)
  private List<MessageProvider> providers = new ArrayList<>();

  private final Map<MessageType, MessageProvider> providersByName = new ConcurrentHashMap<>();

  @PostConstruct
  public void init() {

    for (MessageProvider provider : providers) {
      providersByName.put(provider.getType(), provider);
    }

    for (MessageServicePlugin plugin : plugins) {
      pluginsByType.put(plugin.getType(), plugin);
    }
  }

  public void scheduleMessage(ClientContext client, CRMMessageSystem message) {
    // Validate and save message
    message.setStatus(MessageStatus.PENDING);
    CRMMessageSystem messageInDb = saveMessage(client, message);

    log.info("📅 Scheduled message [id={}] [company={}] type={} for {}",
      messageInDb.getId(),
      client.getCompanyCode(),
      messageInDb.getMessageType(),
      DateUtil.asCompactDateTime(messageInDb.getScheduledAt()));

    // Add to queue if in current session
    queueManager.addMessage(new MessageQueueManager.MessageHolder(messageInDb));
  }

  public void sendMessage(ClientContext client, CRMMessageSystem message) {
    final MessageStatus status = message.getStatus();
    if (status == MessageStatus.CANCELLED) return;

    MessageServicePlugin plugin = pluginsByType.get(message.getPluginName());
    Objects.assertNotNull(plugin, "Message service plugin not found: " + message.getPluginName());
    MessageProvider messageProvider = providersByName.get(message.getMessageType());
    Objects.assertNotNull(messageProvider, "Message provider not found: " + message.getMessageType());

    try {
      message = plugin.onPreSend(client, message);
      CRMMessageSystem messageInDb = messageProvider.send(client, message);
      plugin.onPostSend(client, messageInDb);
    } catch (Exception e) {
      log.error("❌ Failed to send message [id={}] type={}: {}", message.getId(), message.getMessageType(), e.getMessage(), e);
      try {
        plugin.onSendError(client, message, e);
      } catch (Exception e2) {
        log.error("❌ Failed to send error message [id={}] type={}: {}", message.getId(), message.getMessageType(), e2.getMessage(), e2);
        throw new RuntimeException("Failed to send message: " + e.getMessage(), e);
      }
    }
  }

  public List<CRMMessageSystem> findByReferenceAndPlugin(ClientContext client, Long referenceId,
                                                         String referenceType, String pluginName, MessageStatus status) {
    return messageSystemRepo.findByReferenceAndPlugin(referenceId, referenceType, pluginName, status);
  }

  public CRMMessageSystem saveMessage(ClientContext client, CRMMessageSystem message) {
    message.set(client);
    return messageSystemRepo.save(message);
  }

}