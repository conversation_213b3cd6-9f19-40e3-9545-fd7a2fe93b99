package cloud.datatp.fforwarder.price.entity;

public enum StarRating {

  ONE_STAR(1),
  TWO_STARS(2),
  THREE_STARS(3),
  FOUR_STARS(4),
  FIVE_STARS(5);

  private final int value;

  StarRating(int value) {
    this.value = value;
  }

  public int getValue() {
    return value;
  }

  static public StarRating parse(String token) {
    if (token == null) return THREE_STARS;
    try {
      int stars = Integer.parseInt(token.trim());
      return fromValue(stars);
    } catch (NumberFormatException e) {
      for (StarRating rating : values()) {
        if (rating.name().equalsIgnoreCase(token.trim())) return rating;
      }
      return THREE_STARS;
    }
  }

  static public StarRating fromValue(int stars) {
    return switch (stars) {
      case 1 -> ONE_STAR;
      case 2 -> TWO_STARS;
      case 4 -> FOUR_STARS;
      case 5 -> FIVE_STARS;
      default -> THREE_STARS;
    };
  }
}