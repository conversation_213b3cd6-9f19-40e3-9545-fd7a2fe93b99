package cloud.datatp.fforwarder.price.request;

import cloud.datatp.fforwarder.core.message.MailMessageProvider;
import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.price.BulkCargoInquiryRequestLogic;
import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;

import java.util.Date;
import java.util.HashSet;

import net.datatp.module.company.CompanyLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BulkCargoStatusMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "bulk-cargo-status-notification";

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private MailMessageProvider mailMessageProvider;

  @Autowired
  private BulkCargoInquiryRequestLogic bulkCargoInquiryLogic;

  protected BulkCargoStatusMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  public void onPostSend(ClientContext client, CRMMessageSystem message) {
  }

  public void onSendError(ClientContext client, CRMMessageSystem message, Exception error) throws Exception {
    if (message.getStatus() != MessageStatus.CANCELLED && message.getMessageType() == MessageType.ZALO) {
      Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
      Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
      if (!BulkCargoInquiryRequest.TABLE_NAME.equals(message.getReferenceType())) {
        throw new RuntimeException("Message reference type is not BulkCargoInquiryRequest: " + message.getReferenceType());
      }

      BulkCargoInquiryRequest request = bulkCargoInquiryLogic.getBulkCargoInquiryRequest(client, message.getReferenceId());
      Objects.assertNotNull(request, "Bulk cargo inquiry request {} is not found", message.getReferenceId());

      CRMMessageSystem mailMessage = new CRMMessageSystem();
      mailMessage.setReferenceId(message.getReferenceId());
      mailMessage.setReferenceType(message.getReferenceType());
      mailMessage.setMessageType(MessageType.MAIL);
      mailMessage.setContent("<div>Test</div>");
      mailMessage.setPluginName(PLUGIN_TYPE);
      mailMessage.setRecipients(new HashSet<>(request.getToList()));
      mailMessage.setScheduledAt(new Date());

      MapObject metadata = new MapObject();
      metadata.put("fromEmail", "<EMAIL>");
      mailMessage.setMetadata(metadata);

      mailMessageProvider.send(client, mailMessage);
    }
  }

}