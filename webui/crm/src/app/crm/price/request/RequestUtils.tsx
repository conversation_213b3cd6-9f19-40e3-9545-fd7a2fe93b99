import * as FeatherIcon from 'react-feather';


export const RequestStarRating = {
  ONE_STAR: {
    label: '1 Star',
    value: 'ONE_STAR',
    color: 'yellow-light',
    hexColor: '#FFF9C4',
    icon: FeatherIcon.Star,
  },
  TWO_STARS: {
    label: '2 Star',
    value: 'TWO_STARS',
    color: 'yellow',
    hexColor: '#FFEE58',
    icon: FeatherIcon.Star,
  },
  THREE_STARS: {
    label: '3 Star',
    value: 'THREE_STARS',
    color: 'yellow',
    hexColor: '#FDD835',
    icon: FeatherIcon.Star,
  },
  FOUR_STARS: {
    label: '4 Star',
    value: 'FOUR_STARS',
    color: 'yellow-dark',
    hexColor: '#FBC02D',
    icon: FeatherIcon.Star,
  },
  FIVE_STARS: {
    label: '5 Star',
    value: 'FIVE_STARS',
    color: 'yellow-darker',
    hexColor: '#F9A825',
    icon: FeatherIcon.Star,
  }
} as const;
export type RequestStarValue = keyof typeof RequestStarRating;
export interface IRequestStarRating {
  label: string;
  value: RequestStarValue;
  color: string;
  hexColor: string;
  icon: any;
}

export class RequestUtils {


  static getStarRatingInfo(star: string = 'FIVE_STARS') {
    const starsInfo = RequestStarRating[star as keyof typeof RequestStarRating];
    return starsInfo || RequestStarRating.FIVE_STARS;
  }

  static getStarRatingList() {
    return Object.values(RequestStarRating);
  }
}
