import React, { RefObject } from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, sql, bs, app, server, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { BulkCargoInquiryStatusUtils, BulkCargoProcessStatus, BulkCargoProcessStatusUtils, T } from '../backend';

import BBRefLocation = module.settings.BBRefLocation;
import { BBRefBFSOnePartner } from '../../partner';
import { BBRefCommodityType } from 'app/crm/common/commodity';
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';
import { RequestUtils } from './RequestUtils';

const SESSION = app.host.DATATP_SESSION;
export interface UICheckRequestListEditorProps extends entity.DbEntityListProps {
  onPostCommit?: (entity: any, uiEditor?: app.AppComponent) => void;
}
export class UICheckRequestListEditor extends entity.DbEntityList<UICheckRequestListEditorProps> {

  createVGridConfig(): grid.VGridConfig {
    const { pageContext, appContext } = this.props;

    const onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      if (oldVal === newVal) return;
      const { displayRecord, fieldConfig } = ctx;
      let record = displayRecord.record || {}
      record[fieldConfig.name] = newVal;
      ctx.displayRecord.getRecordState().markModified();
      ctx.gridContext.getVGrid().forceUpdateView();
    }

    const CELL_HEIGHT: number = 30;
    let config: grid.VGridConfig = {

      record: {
        dataCellHeight: CELL_HEIGHT,
        editor: {
          enable: true,
          supportViewMode: ['table'],
        },
        control: {
          width: 60,
          items: [
            {
              name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let uiList = ctx.uiRoot as UICheckRequestListEditor;
                let { plugin } = uiList.props;
                plugin.getListModel().removeRecord(dRecord.record);
                ctx.getVGrid().forceUpdateView();
              },
            },
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let uiList = ctx.uiRoot as UICheckRequestListEditor;
                uiList.onCopy(dRecord);
              },
            },
          ]
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'customer', label: T(`Customer/ Lead...`), width: 270, state: { showRecordState: true },
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                return (
                  <BBRefBFSOnePartner tabIndex={tabIndex} autofocus={focus}
                    placeholder='Customer/ Lead...' allowUserInput style={{ minHeight: CELL_HEIGHT, minWidth: 270 }}
                    placement='auto-start'
                    appContext={appContext} pageContext={pageContext} minWidth={400} hideMoreInfo
                    bean={record} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'} partnerType='Customer'
                  />
                );
              }
            }
          },
          {
            name: 'salemanLabel', label: T(`Saleman`), width: 270, state: { showRecordState: true },
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                return (
                  <BBRefCrmUserRole style={{ minHeight: CELL_HEIGHT, minWidth: 250 }} required minWidth={400}
                    appContext={appContext} pageContext={pageContext} bean={record} tabIndex={tabIndex} autofocus={focus}
                    beanIdField='salemanAccountId' beanLabelField='salemanLabel'
                    placeholder='Saleman.' hideMoreInfo
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      bean['salemanAccountId'] = selectOpt['accountId'];
                      bean['salemanLabel'] = selectOpt['fullName'];
                      bean['salemanEmail'] = selectOpt['email'];
                      bean['salemanPhone'] = selectOpt['phone'];
                    }} />
                );
              }
            }
          },
          {
            name: 'pricingDate', label: T(`Created Date`), width: 120,
            format: util.text.formater.compactDate,
            editor: {
              type: 'date', enable: true,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig, gridContext } = ctx;
                let event: grid.VGridCellEvent = {
                  row: displayRecord.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                gridContext.broadcastCellEvent(event);
              },
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus, fieldConfig } = ctx;
                let record = displayRecord.record;
                return (
                  <input.BBDateInputMask style={{ height: CELL_HEIGHT }}
                    bean={record} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={_onInputChange} />
                )
              }
            }
          },
          {
            name: 'requestDate', label: T(`Request Date`), width: 120,
            format: util.text.formater.compactDate,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                if ((event.field.name === 'pricingDate')) {
                  _rowRecord['requestDate'] = _rowRecord['pricingDate']
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'date', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus, fieldConfig } = ctx;
                let record = displayRecord.record;
                return (
                  <input.BBDateInputMask style={{ height: CELL_HEIGHT }}
                    bean={record} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={_onInputChange} />
                )
              }
            }
          },
          {
            name: 'laycan', label: T(`Laycan`), width: 180, style: { height: CELL_HEIGHT },
            format: util.text.formater.compactDate,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                if ((event.field.name === 'pricingDate')) {
                  _rowRecord['laydaysDate'] = _rowRecord['pricingDate']
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'date', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                return (
                  <div className='flex-hbox'>
                    <input.BBDateInputMask style={{ height: CELL_HEIGHT }} className='me-2'
                      bean={record} field={'laydaysDate'} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                      onInputChange={_onInputChange} />
                    <input.BBDateInputMask style={{ height: CELL_HEIGHT }}
                      bean={record} field={'cancellingDate'} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                      onInputChange={_onInputChange} />
                  </div>
                )
              }
            }
          },
          {
            name: 'cargoType', label: T(`Cargo Type`), style: { height: CELL_HEIGHT },
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} bean={record} field={'cargoType'}
                    options={['BULK', 'PACKAGE', 'EQUIPMENTS']}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (newVal) {
                        bean[field] = newVal;
                        _onInputChange(bean, field, oldVal, newVal);
                      }
                    }} />
                )
              }
            }
          },
          {
            name: 'termOfService', label: T(`Term`), width: 80,
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                if (!record.termOfService) {
                  record.termOfService = 'FLT';
                }
                const termOfServiceOptions: string[] = [
                  'FLT', 'FIO', 'LIFO', 'FILO', 'CROSS BORDER', 'EXW', 'RORO'
                ];
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} className='flex-hbox h-100 w-100'
                    bean={record} field={'termOfService'} options={termOfServiceOptions} style={{ height: CELL_HEIGHT }}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (newVal) {
                        bean[field] = newVal;
                        _onInputChange(bean, field, oldVal, newVal);
                      }
                    }} />
                )
              }
            }
          },
          {
            name: 'cargoProceeding', label: T(`Cargo Proceeding`), width: 150,
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                const options = [
                  'Bidding cargo',
                  'Comparing with container method cost',
                  'Referring for trading contract',
                  'Ready to load (complete trading contract and done LC)'];
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} className='flex-hbox h-100 w-100'
                    bean={record} field={'cargoProceeding'} options={options} style={{ height: CELL_HEIGHT }}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (newVal) {
                        bean[field] = newVal;
                        _onInputChange(bean, field, oldVal, newVal);
                      }
                    }} />
                )
              }
            }
          },
          {
            name: 'fromLocationLabel', label: T('POL'), hint: T("Port of Loading"), width: 220,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                if ((event.field.name === 'typeOfShipment')) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: "string",
              enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let oldVal = record[fieldConfig.name];
                let locationTypes: module.settings.LocationType[] = ['Port'];
                return (
                  <BBRefLocation style={{ minHeight: CELL_HEIGHT, minWidth: 320 }} className='flex-hbox'
                    placeholder='Port of Loading' autofocus={focus} hideMoreInfo
                    appContext={appContext} pageContext={pageContext} bean={record}
                    beanIdField={fieldConfig.name} beanLabelField={'fromLocationLabel'}
                    tabIndex={tabIndex} locationTypes={locationTypes} refLocationBy='code'
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => {
                      bean['fromLocationCode'] = _selectOpt['code'];
                      bean['fromLocationLabel'] = _selectOpt['label'];
                      onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name])
                    }} />
                )
              },
              onInputChange: onInputChange
            }
          },
          {
            name: 'toLocationCode', label: T('POD'), hint: T("Port of Discharge"), width: 220,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                if ((event.field.name === 'typeOfShipment')) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: "String",
              enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let oldVal = record[fieldConfig.name];
                let locationTypes: module.settings.LocationType[] = ['Port'];
                return (
                  <BBRefLocation style={{ minHeight: CELL_HEIGHT, minWidth: 320 }}
                    placeholder='Port of Discharge' autofocus={focus} hideMoreInfo
                    appContext={appContext} pageContext={pageContext} bean={record}
                    beanIdField={fieldConfig.name} beanLabelField={'toLocationLabel'}
                    tabIndex={tabIndex} locationTypes={locationTypes} refLocationBy='code'
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name])} />
                )
              },
              onInputChange: onInputChange
            }
          },
          {
            name: 'quantity', label: T(`Quantity`), width: 120, style: { height: CELL_HEIGHT },
            editor: { type: 'number', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'volume', label: T(`Volume`), width: 120, style: { height: CELL_HEIGHT },
            editor: { type: 'number', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'stowageFactor', label: T(`Stowage Factor (CBM/MT)`), width: 120, style: { height: CELL_HEIGHT },
            editor: { type: 'number', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'commodity', label: T(`Commodity`), width: 180, style: { height: CELL_HEIGHT },
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, fieldConfig, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                return (
                  <BBRefCommodityType tabIndex={tabIndex} autofocus={focus} minHeight={600}
                    appContext={appContext} pageContext={pageContext} hideMoreInfo
                    bean={record} placeholder='Commodity' beanIdField='commodity'
                    onPostUpdate={
                      (_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) =>
                        _onInputChange(bean, fieldConfig.name, '', record.commodity)
                    }
                  />
                );
              }
            }
          },
          {
            name: 'descOfGoods', label: T(`Desc Of Goods`), width: 170, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'loadRate', label: T(`Load Rate`), width: 120, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'dischargeRate', label: T(`Discharge Rate`), width: 120, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'stackable', label: T(`Stackable`), width: 120, style: { height: CELL_HEIGHT },
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                if (!record['stackable']) {
                  record['stackable'] = 'NON_STACKABLE';
                }

                return (
                  <input.BBSelectField bean={record} field={'stackable'} tabIndex={tabIndex} focus={focus}
                    optionLabels={['Stackable', 'Non Stackable']}
                    options={['STACKABLE', 'NON_STACKABLE']} onInputChange={_onInputChange} />
                )
              }

            }
          },
          {
            name: 'stackableType', label: T(`Stackable Type`), width: 120, style: { height: CELL_HEIGHT },
            editor: {
              type: 'string', enable: true, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                return (
                  <input.BBSelectField bean={record} field={'stackableType'} tabIndex={tabIndex} focus={focus}
                    optionLabels={['', 'On deck', 'Under deck']}
                    options={[null, 'ON_DECK', 'UNDER_DECK']} onInputChange={_onInputChange} />
                )
              }

            }
          },
          {
            name: 'allowCombine', label: T(`Allow Combine`), width: 120, cssClass: 'flex-vbox align-items-center',
            editor: {
              type: 'boolean', enable: true, onInputChange: onInputChange,
            }
          },
          {
            name: 'note', label: T(`Note (Sales)`), width: 170, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'pricingNote', label: T(`Note (Pricing)`), width: 170, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true, onInputChange: onInputChange, }
          },
          {
            name: 'status', label: T(`Status`), width: 170, style: { height: CELL_HEIGHT }, container: 'fixed-right',
            editor: {
              type: 'string', enable: true,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let currentStatus = BulkCargoInquiryStatusUtils.getStatusInfo(record['status']);
                let StatusIcon = currentStatus.icon;
                let label = currentStatus.label;
                let color = currentStatus.color;

                const statusList = BulkCargoInquiryStatusUtils.getStatus();
                const statusRemaining = statusList.filter(status =>
                  status.value !== record['status'] &&
                  status.value !== 'IN_PROGRESS'
                );

                return (
                  <bs.Popover className="d-flex flex-center w-100 h-100"
                    title={T('Status')} closeOnTrigger=".btn" >
                    <bs.PopoverToggle
                      className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                      <StatusIcon size={14} className="me-1" />
                      <span>{label}</span>
                    </bs.PopoverToggle>
                    <bs.PopoverContent>
                      <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                        {statusRemaining.map(opt => {
                          let OptIcon = opt.icon;
                          return (
                            <div key={opt.value}
                              className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                              onClick={() => {
                                record['status'] = opt.value;
                                onInputChange(record, 'status', '', opt.value);
                              }
                              }>
                              <OptIcon size={14} className="me-1" />
                              <span>{opt.label}</span>
                            </div>
                          );
                        })}
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>
                );
              }
            }
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      footer: {
        default: {
          render: (ctx: grid.VGridContext) => {
            return (
              <bs.Toolbar className='border'>
                <entity.WButtonEntityWrite outline
                  appContext={appContext} pageContext={pageContext}
                  icon={FeatherIcon.Save} label={T('Save Changes')}
                  onClick={this.onSaveChanges} />
              </bs.Toolbar>
            );
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  onSaveChanges = () => {
    let { appContext, onPostCommit } = this.props;
    let records = this.vgridContext.model.getRecords()

    if (records.length === 0) {
      let messageError: string = 'No records found. Please add at least one record before saving.'
      bs.dialogShow('Error',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{messageError}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    const missingSalemanAccountId = records.some(record => !record.salemanAccountId);
    if (missingSalemanAccountId) {
      let messageError: string = 'Please enter the salesman before saving.'
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{messageError}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    const missingPort = records.some(record => {
      const isFromLocationMissing = !record['fromLocationCode'] || !record['fromLocationLabel'];
      const isToLocationMissing = !record['toLocationCode'] || !record['toLocationLabel'];
      return (isFromLocationMissing || isToLocationMissing);
    });

    if (missingPort) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide both Port Of Loading and Port Of Discharge .')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    appContext
      .createHttpBackendCall('TransportPriceMiscService', 'saveBulkCargoInquiryRequestRecords', { records: records })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", `Edit Rows Success`);
        this.vgridContext.model.update([]);
        this.vgridContext.getVGrid().forceUpdateView();
        if (onPostCommit) onPostCommit(data, this);
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call()
  }

  componentDidMount(): void {
    this.onAdd();
  }

  onDelete = () => {
    let { plugin } = this.props;
    plugin.listModel.removeSelectedDisplayRecords();
    this.forceUpdate();
  }

  onCopy(dRecord: grid.DisplayRecord): any {
    let clone = { ...dRecord.record }
    this.getVGridContext().model.insertDisplayRecordAt(dRecord.row, clone);
    grid.initRecordState(clone, dRecord.row).markNew();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onAdd = () => {
    let today: string = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    let newBean = {
      laydaysDate: today,
      requestDate: today,
      pricingDate: today,
      typeOfShipment: 'FCL Imp',
      volumeInfo: '1x20DC'
    }
    this.vgridContext.model.addRecord(newBean);
    grid.initRecordState(newBean, 0).markNew();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;

    return (
      <div className='flex-vbox'>

        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>
          <div className='flex-hbox'>
            <grid.WGridFilter context={this.vgridContext} />
          </div>

          <div className="flex-hbox justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 p-1" outline onClick={this.onAdd}>
              <FeatherIcon.Plus size={12} /> Add
            </bs.Button>

            <bs.Button laf='warning' className="border-0 p-1" outline onClick={this.onDelete}>
              <FeatherIcon.Plus size={12} /> Del
            </bs.Button>

          </div>

        </div>
        {this.renderUIGrid()}
      </div>
    )
  }

}

export class UICheckRequestEditor extends entity.AppDbComplexEntityEditor {

  onModify = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    if ((_field === 'mailCcBeans' || _field === 'mailToBeans') && newVal && Object.keys(newVal).length > 0) {
      const emailList: any[] = bean[_field] || [];
      const displayName = newVal['fullName'] || newVal['zaloDisplayName'] || newVal['loginId'];
      const emailAddress = newVal['email'];
      const label = `${displayName} ( ${emailAddress} )`;

      if (displayName) {
        // Filter out any items with an id property that has a value
        const filteredList = emailList.filter(item => !item.id);
        filteredList.push({ label: label, email: emailAddress });
        bean[_field] = filteredList;
      }
    }
    this.nextViewId();
    this.forceUpdate();
  }

  onSave = () => {
    let { appContext, observer, onPostCommit } = this.props;
    let record = observer.getMutableBean();

    const validateRequiredFields = () => {

      const isFromLocationMissing = !record['fromLocationCode'] || !record['fromLocationLabel'];
      const isToLocationMissing = !record['toLocationCode'] || !record['toLocationLabel'];
      const missingPort = isFromLocationMissing || isToLocationMissing;
      if (missingPort) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please provide both Port Of Loading and Port Of Discharge.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingSalemanAccountId = !record['salemanAccountId'];
      if (missingSalemanAccountId) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the salesman before saving.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingLaycan = !record['laydaysDate'] || !record['cancellingDate'];
      if (missingLaycan) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Laycan.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingDescOfGoods = !record['descOfGoods'];
      if (missingDescOfGoods) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Desc Of Goods.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingQuantity = !record['quantity'];
      if (missingQuantity) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Quantity.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingStowageFactor = !record['stowageFactor'];
      if (record['cargoType'] === 'BULK' && missingStowageFactor) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Stowage Factor.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingVolume = !record['volume'];
      if (record['cargoType'] != 'BULK' && missingVolume) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Volume.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingDim = !record['dim'];
      if (record['cargoType'] === 'PACKAGE' && missingDim) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Dim.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingRates = !record['loadRate'] || !record['dischargeRate'];
      if (missingRates) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Load Rate and Discharge Rate.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      return true;
    };

    const saveData = () => {
      appContext.createHttpBackendCall('TransportPriceMiscService', 'saveBulkCargoInquiryRequestRecords', { records: [record] })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", `Save Bulk Cargo Success`);
          observer.replaceBeanProperty('id', undefined)
          if (onPostCommit) {
            onPostCommit(record);
          }
        })
        .withFail((response: server.BackendResponse) => {
          let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
          bs.dialogShow('Error',
            <div className="text-danger fw-bold text-center py-3 border-bottom">
              <FeatherIcon.AlertCircle className="mx-2" />{messageError}
            </div>,
            { backdrop: 'static', size: 'md' }
          );
          return;
        })
        .call();
    };

    const checkNote = () => {
      const missingNote = !record['note'];
      if (missingNote) {
        let messageEle = (<div className="text-warning">You forgot to enter the Note field. Are you sure you want to proceed with this request?</div>);
        bs.dialogConfirmMessage(T("Confirm Save"), messageEle, saveData);
      } else {
        saveData();
      }
    };

    const checkTargetRate = () => {
      const missingTargetRate = !record['targetRate'];
      if (missingTargetRate) {
        let messageEle = (
          <div className="text-warning">You forgot to enter the Target rate field, which makes price checking take longer.
            Are you sure you want to proceed with this request?</div>);
        bs.dialogConfirmMessage(T("Confirm Save"), messageEle, checkNote);
      } else {
        checkNote();
      }
    };

    if (validateRequiredFields()) {
      const missingTargetRate = !record['targetRate'];
      const missingNote = !record['note'];

      if (!missingTargetRate && !missingNote) {
        saveData();
      } else if (missingTargetRate) {
        checkTargetRate();
      } else {
        checkNote();
      }
    }
  }

  render(): React.ReactNode {
    const { appContext, pageContext, observer } = this.props;

    let cargoInquiry = observer.getMutableBean();
    let locationTypes: any[] = ['Port'];
    const termOfServiceOptions: string[] = ['FIO', 'FLT', 'LIFO', 'FILO', 'CROSS BORDER', 'EXW', 'RORO'];
    const termOfServiceLabels: string[] = [
      'FIO (Ocean freight only)',
      'FLT (Ocean freight and loading, discharging cost)',
      'FILO (Ocean freight and discharging cost)',
      'LIFO (Ocean freight and loading cost)',
      'CROSS BORDER',
      'EXW',
      'RORO'
    ];
    const cargoTypeLabelOptions: string[] = ['BULK (Clinker, sand, maize...)', 'PACKAGE (Rice in bag, wood in bundle...)', 'EQUIPMENTS', 'VEHICLE', 'STEEL PRODUCT', 'LIVE ANIMAL', 'SHIPPING AGENCY'];
    const cargoTypeOptions: string[] = ['BULK', 'PACKAGE', 'EQUIPMENTS', 'VEHICLE', 'STEEL_PRODUCT', 'LIVE_ANIMAL', 'SHIPPING_AGENCY'];
    const cargoProceedingOptions: string[] = [
      'Ready to load (complete trading contract and done LC)',
      'Bidding cargo',
      'Comparing with container method cost',
      'Referring for trading contract',
    ]
    const purposeOptions: string[] = ['IMPORT', 'EXPORT', 'DOMESTIC'];

    const salemanBranchLabelOptions: string[] = ['Bee HPH', 'Bee HCM', 'Bee HAN', 'Bee DAD', 'Bee Corp',
      'CAM RANH', 'QUANG NGAI', 'QUY NHON', 'NAM DINH', 'HA NAM', 'HAI DUONG', 'NGHE AN',
      'THANH HOA', 'BAC NINH', 'LANG SON', 'THAI NGUYEN', 'BINH DUONG', 'CAN THO', 'DONG NAI'
    ];
    const salemanBranchOptions: string[] = ['beehph', 'beehcm', 'beehan', 'beedad', 'bee',
      'camranh', 'quangngai', 'quynhon', 'namdinh', 'hanam', 'haiduong', 'nghean',
      'thanhhoa', 'bacninh', 'langson', 'thainguyen', 'binhduong', 'cantho', 'dongnai'
    ];

    let cargoType = cargoInquiry.cargoType;

    return (
      <div className='flex-vbox rounded-bottom mx-md-2 px-md-2'>
        <div className='flex-grow-1'>

          <bs.Row>
            <bs.Col span={12} md={6}>
              <BBRefLocation appContext={appContext} pageContext={pageContext} bean={cargoInquiry}
                beanIdField={'fromLocationCode'} beanLabelField={'fromLocationLabel'} locationTypes={locationTypes}
                hideMoreInfo
                label='Port Of Loading' placeholder='' refLocationBy='code' style={{ minWidth: 350 }}
                onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                  bean['fromLocationCode'] = selectOpt['code'];
                  bean['fromLocationLabel'] = selectOpt['label'];
                }} />
            </bs.Col>

            <bs.Col span={12} md={6}>
              <BBRefLocation appContext={appContext} pageContext={pageContext} bean={cargoInquiry}
                beanIdField={'toLocationCode'} beanLabelField={'toLocationLabel'} locationTypes={locationTypes}
                hideMoreInfo
                label='Port Of Discharge' placeholder='' refLocationBy='code' style={{ minWidth: 350 }}
                onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                  bean['toLocationCode'] = selectOpt['code'];
                  bean['toLocationLabel'] = selectOpt['label'];
                }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col md={3}>
              <input.BBSelectField bean={cargoInquiry} label={T('Saleman Branch')} field={'salemanBranchName'}
                options={salemanBranchOptions} optionLabels={salemanBranchLabelOptions} />
            </bs.Col>

            <bs.Col md={3}>
              <BBRefCrmUserRole
                appContext={appContext} pageContext={pageContext} bean={cargoInquiry}
                beanIdField='salemanAccountId' beanLabelField='salemanLabel'
                placeholder='Saleman.' label='Saleman' hideMoreInfo minWidth={500}
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['salemanAccountId'] = selectOpt['id'];
                  bean['salemanLabel'] = selectOpt['fullName'];
                  bean['salemanEmail'] = selectOpt['email'];
                  bean['salemanPhone'] = selectOpt['phone'];
                }} />
            </bs.Col>

            <bs.Col span={6}>
              <BBRefBFSOnePartner placeholder='Customer/ Lead...' label='Customer/ Lead' allowUserInput
                style={{ width: '100%' }} placement='auto-start'
                appContext={appContext} pageContext={pageContext} minWidth={400} hideMoreInfo
                bean={cargoInquiry} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'} partnerType='Customer'
              />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6} md={2}>
              <input.BBSelectField bean={cargoInquiry} label={T('Cargo Type')} field={'cargoType'}
                options={cargoTypeOptions} optionLabels={cargoTypeLabelOptions}
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  this.forceUpdate();
                  if (newVal === 'BULK') cargoInquiry.unit = 'TNE';
                }} />
            </bs.Col>

            {cargoType === 'BULK' ? (
              <>
                <bs.Col span={6} md={2}>
                  <input.BBNumberField bean={cargoInquiry} label={T(`Quantity (Metric Ton)`)} field={'quantity'} />
                </bs.Col>
                <bs.Col span={6} md={2}>
                  <bs.CssTooltip position='top-left' width={360} >
                    <bs.CssTooltipToggle >
                      <input.BBNumberField bean={cargoInquiry} label={T("Stowage Factor (CBM/MT)")} field={'stowageFactor'} />
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent className="d-flex flex-column rounded" >
                      <div className="tooltip-body" style={{ color: '#198754' }}>
                        What is the volume (in CBM) per metric ton of cargo?
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                </bs.Col>
              </>
            ) : (
              <>
                <bs.Col span={6} md={2}>
                  <input.BBNumberField bean={cargoInquiry} label={T(`Quantity (Metric Ton)`)} field={'quantity'} />
                </bs.Col>
                <bs.Col span={6} md={2}>
                  <input.BBNumberField bean={cargoInquiry} label={T(`Volume (CBM)`)} field={'volume'} />
                </bs.Col>
              </>
            )}

            <bs.Col md={4}>
              <div className='bb-field'>
                <bs.FormLabel>Laycan</bs.FormLabel>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBDateTimeField
                      label={T('')} bean={cargoInquiry}
                      field={'laydaysDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false} />
                  </div>
                  <div className='w-50'>
                    <input.BBDateTimeField
                      label={T('')} bean={cargoInquiry}
                      field={'cancellingDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false} />
                  </div>
                </div>
              </div>
            </bs.Col>

            <bs.Col md={2}>
              <input.BBSelectField bean={cargoInquiry} label={T('Purpose')} field={'purpose'}
                options={purposeOptions} />
            </bs.Col>
          </bs.Row>

          <bs.Row className='pt-md-1'>
            <bs.Col span={6}>
              <BBRefCommodityType appContext={appContext} pageContext={pageContext} hideMoreInfo
                bean={cargoInquiry} placeholder='Commodity' label='Commodities / Descriptions Of Goods' beanIdField='commodity' />
              <input.BBTextField bean={cargoInquiry} field={"descOfGoods"} style={{ height: '3.9em' }} />
            </bs.Col>

            <bs.Col span={6}>
              <div className='flex-hbox'>
                <div className='w-50 me-2'>
                  <input.BBSelectField bean={cargoInquiry} label={T('Term of Service')} field={'termOfService'}
                    options={termOfServiceOptions} optionLabels={termOfServiceLabels} />
                </div>
                <div className='w-50'>
                  <input.BBSelectField bean={cargoInquiry} label={T('Cargo Proceeding')} field={'cargoProceeding'}
                    options={cargoProceedingOptions} />
                </div>
              </div>
              {cargoType === 'PACKAGE' ? (
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBStringField bean={cargoInquiry} label={T('DIM')} field={'dim'} />
                  </div>
                  <div className='w-50'>
                    <bs.CssTooltip position='top-left' width={360} >
                      <bs.CssTooltipToggle >
                        <input.BBNumberField bean={cargoInquiry} label={T("Stowage Factor (CBM/MT)")} field={'stowageFactor'} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-body" style={{ color: '#198754' }}>
                          What is the volume (in CBM) per metric ton of cargo?
                        </div>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  </div>
                </div>
              ) : (
                cargoType === 'EQUIPMENTS' || cargoType === 'VEHICLE' || cargoType === 'STEEL_PRODUCT' ? (
                  <div className='flex-hbox'>
                    <div className='w-50 me-2'>
                      <input.BBSelectField bean={cargoInquiry} field={'stackable'} label={T('Stackable/Non Stackable')}
                        optionLabels={['Stackable', 'Non Stackable']} options={['STACKABLE', 'NON_STACKABLE']}
                        onInputChange={this.onModify} />
                    </div>
                    <div className='w-50'>
                      <div className='flex-hbox'>
                        <div className='w-50 me-2'>
                          <input.BBSelectField bean={cargoInquiry} field={'stackableType'} label={T('Stowage Option')}
                            optionLabels={['On deck', 'Under deck']}
                            options={['ON_DECK', 'UNDER_DECK']} />
                        </div>
                        <div className='w-50 flex-vbox align-items-center justify-content-end'>
                          <input.BBCheckboxField bean={cargoInquiry} field={'allowCombine'} label={T('Allow Combine')} value={false} />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : null
              )}
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField className='flex-vbox' label={T('Load Rate (MT/Day)')} bean={cargoInquiry} field={'loadRate'}
                placeholder='How many tons can the shipper load per day?' onInputChange={this.onModify} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField className='flex-vbox' label={T('Discharge Rate (MT/Day)')} bean={cargoInquiry} field={'dischargeRate'}
                placeholder='How many tons can the consignee discharge per day?' onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>

          <bs.Row className='my-md-2'>
            <bs.Col span={12} md={6}>
              <input.BBTextField className='flex-vbox' label='Other request' bean={cargoInquiry} field={'note'} style={{ height: '5rem' }}
                placeholder='Other information, if any' onInputChange={this.onModify} />
            </bs.Col>
            <bs.Col span={12} md={6}>
              <input.BBTextField style={{ height: '5rem' }}
                bean={cargoInquiry} label={T('Target rate (if any)')} field={'targetRate'} placeholder='Rate idea (If any)'
                onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>
        </div>
        <bs.Toolbar className='border' >
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            icon={FeatherIcon.Save} label={T('Save')}
            onClick={this.onSave}
          />
        </bs.Toolbar>
      </div >
    )
  }
}

export class UIBulkCargoInquiryRequestReportPlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System') {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TransportPriceMiscService',
      searchMethod: 'searchBulkCargoInquiryRequests',
    }

    let status = BulkCargoInquiryStatusUtils.getStatus();
    let statusOpts = status.map(opt => opt.value);
    let statusOptLabels = status.map(opt => opt.label);

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("requestDate", T("Request Date")),
        ...sql.createDateTimeFilter("pricingDate", T("Pricing Date")),
      ],
      optionFilters: [
        {
          name: "status", "label": "Status", "type": "STRING", "required": true,
          options: statusOpts,
          optionLabels: statusOptLabels,
          multiple: true,
          selectOptions: statusOpts
        },
        sql.createStorageStateFilter(["ACTIVE", "ARCHIVED"])
      ],
      maxReturn: 500
    }
  }

  withSearchPattern(pattern: string) {
    if (!this.searchParams) throw new Error("Need to config search params");
    const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
    if (searchFilter) {
      searchFilter.filterValue = pattern
    } else if (this.searchParams.filters) {
      this.searchParams.filters = [...sql.createSearchFilter(pattern)];
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export class UIBulkCargoInquiryRequestTrackingList extends entity.DbEntityList {
  selectedDisplayRecord: grid.DisplayRecord | null = null;
  currentKeyTrackerMap: Record<string, any> = {};

  createVGridConfig(): grid.VGridConfig {
    const { appContext, pageContext, readOnly } = this.props;
    let moderatorCap = pageContext.hasUserModeratorCapability();
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly

    const renderTooltipAdvanced = (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      const uiList = ctx.uiRoot as UIBulkCargoInquiryRequestTrackingList;
      const record = dRecord.record;
      const val = record[field.name] || 'N/A';
      const cargoType = record['cargoType'];

      let mailTo = (record['mailTo'] || '').split(",").map((item: string) => item.trim()).join("\n");
      record['mailTo'] = mailTo

      let descOfGoods = record['commodity'] ? record['commodity'] + '\n' + (record['descOfGoods'] || '') : (record['descOfGoods'] || '')
      let quantity = record['quantity'] && record['quantity'] != 0 ? record['quantity'] + 'MT' : '';
      let volume = record['volume'] && record['quantity'] != 0 ? record['volume'] + 'CBM' : '';
      let stowageFactor = record['stowageFactor'] ? record['stowageFactor'] + 'CBM/MT' : '';

      let laycan = util.text.formater.compactDate(record['laydaysDate']) + ' - ' + util.text.formater.compactDate(record['cancellingDate']);
      let stackable = record['stackable'] === 'STACKABLE' ? 'Stackable' : 'Non Stackable';;
      let stackableType = record['stackableType'] === 'ON_DECK' ? 'On deck' : 'Under deck';
      let allowCombine = record['allowCombine'] ? 'Yes' : 'No';

      let loadingPort = record['fromLocationLabel']
      let dischargePort = record['toLocationLabel']
      let loadRate = record['loadRate']
      let dischargeRate = record['dischargeRate'];
      let rate = '';
      if (loadRate && dischargeRate) {
        rate = `${loadRate} / ${dischargeRate}`;
      } else if (loadRate) {
        rate = loadRate;
      } else if (dischargeRate) {
        rate = dischargeRate;
      }
      let note = record['note'] || '';

      const handleClick = () => {
        uiList.onDefaultSelect(dRecord);
        let textFormat = '';
        if (descOfGoods) {
          textFormat += `Description: ${descOfGoods}\n`;
        }
        if (cargoType === 'BULK' || cargoType === 'PACKAGE') {
          textFormat += `GW: ${quantity} - SF: ${stowageFactor}\n`;
        } else {
          if (volume) {
            textFormat += `GW: ${quantity} - Volume: ${volume}\n`;
          } else {
            textFormat += `GW: ${quantity}\n`;
          }
        }
        textFormat += `Laycan: ${laycan}\n`;
        if (cargoType === 'EQUIPMENTS' || cargoType === 'VEHICLE' || cargoType === 'STEEL_PRODUCT') {
          textFormat += `Stackable: ${stackable} \n`
          textFormat += `Stowage Option: ${stackableType}\n`;
          textFormat += `Allow Combine: ${allowCombine}\n`;
        }
        textFormat += `Loading Port: ${loadingPort}\n`;
        textFormat += `Discharge Port: ${dischargePort}\n`;
        if (rate) {
          textFormat += `Load/Discharge Rate: ${rate}\n`;
        }
        if (note) {
          textFormat += `Note: ${note}\n`;
        }

        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      return (
        <bs.CssTooltip width={400} position='bottom-right' offset={{ x: 380, y: 0 }}>
          <bs.CssTooltipToggle>
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent className='p-0'>
            <div className="tooltip-header" style={{ fontSize: '0.95em', color: '#198754' }}>
              {descOfGoods && <div><span className="fw-bold">Description: </span>{descOfGoods}</div>}
              {cargoType === 'BULK' || cargoType === 'PACKAGE' ? (
                <div>
                  <span className="fw-bold">GW: </span>{quantity}<span className="fw-bold"> - SF: </span>{stowageFactor}
                </div>
              ) : (
                volume ?
                  <div>
                    <span className="fw-bold">GW: </span>{quantity}<span className="fw-bold"> - Volume: </span>{volume}
                  </div>
                  :
                  <div><span className="fw-bold">GW: </span>{quantity}</div>
              )}
              <div><span className="fw-bold">Laycan: </span>{laycan}</div>
              {(cargoType === 'EQUIPMENTS' || cargoType === 'VEHICLE' || cargoType === 'STEEL_PRODUCT') && (
                <div>
                  <div><span className="fw-bold">Stackable: </span>{stackable}</div>
                  <div><span className="fw-bold">Stowage Option: </span>{stackableType}</div>
                  <div><span className="fw-bold">Allow Combine: </span>{allowCombine}</div>
                </div>
              )}
              <div><span className="fw-bold">Loading Port: </span>{loadingPort}</div>
              <div><span className="fw-bold">Discharge Port: </span>{dischargePort}</div>
              {rate && <div><span className="fw-bold">Load/Discharge Rate: </span>{rate}</div>}
              {note && <div><span className="fw-bold">Note: </span>{note}</div>}
            </div>
          </bs.CssTooltipContent>
        </bs.CssTooltip >
      );
    }


    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: (writeCap) || moderatorCap,
        },
        control: {
          width: 30,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let uiList = ctx.uiRoot as UIBulkCargoInquiryRequestTrackingList;
                uiList.onCopy(dRecord);
              },
            },
          ]
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),

          {
            name: 'processStatus', label: T('Process'), width: 120, filterable: true, container: 'fixed-left',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let fieldName: string = event.field.name;
                if (fieldName === 'processStatus') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let currentProcessStatus = BulkCargoProcessStatusUtils.getStatusInfo(record['processStatus']);
              let StatusIcon = currentProcessStatus.icon;
              let label = currentProcessStatus.label;
              let color = currentProcessStatus.color;

              const statusList = BulkCargoProcessStatusUtils.getStatusList();
              const statusRemaining = statusList.filter(processStatus =>
                processStatus.value !== record['processStatus']
              );

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100"
                  title={T('Process Status')} closeOnTrigger=".btn" >
                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => {
                              record['processStatus'] = opt.value;
                              this.saveChanges(record)
                              let event: grid.VGridCellEvent = {
                                row: record.row,
                                field: _field,
                                event: 'Modified'
                              }
                              this.vgridContext.broadcastCellEvent(event);
                            }
                            }>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },
          {
            name: 'code', label: T(`Ref`), width: 125, container: 'fixed-left', filterable: true,
            computeCssClasses: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              if (dRecord.getRecordState().isMarkModified()) return 'fw-bold cell-selected'
              return ''
            },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'clientLabel', label: T(`Customer/ Lead...`), width: 180, state: { showRecordState: true },
            customRender: renderTooltipAdvanced,
          },
          {
            name: "salemanLabel", label: 'Saleman.', width: 180, filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;

              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length > 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              return (
                <div className='flex-hbox justify-content-center align-items-center' style={{ cursor: 'pointer', userSelect: 'text' }}
                  onClick={() => this.onDefaultSelect(dRecord)}>
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'salemanBranchName', label: T('Saleman Branch'), width: 120,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'cargoType', label: T(`Cargo Type`), width: 120, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'requestDate', label: T(`Request Date`), width: 120, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            fieldDataGetter(record: any) {
              let val: string = record['requestDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'termOfService', label: T(`Term.`), width: 90, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'cargoProceeding', label: T(`Proceeding`), width: 120, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'fromLocationLabel', label: T('From Location'), width: 200, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'toLocationLabel', label: T('To Location'), width: 200, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'laycan', label: T(`Laycan`), width: 180,
            fieldDataGetter(record: any) {
              let laydaysDate: string = record['laydaysDate']
              let cancellingDate: string = record['cancellingDate']
              let val = ''
              if (laydaysDate) val += util.text.formater.compactDate(laydaysDate);
              if (cancellingDate) val += ' - ' + util.text.formater.compactDate(cancellingDate);
              return val
            },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'quantity', label: T(`Quantity`), width: 120,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'volume', label: T(`Volume`), width: 120,
            fieldDataGetter(record: any) {
              let volume = record['volume'];
              let unit = record['unit'];
              let val = '';
              if (volume) val += volume;
              if (unit) val += ' (' + unit + ')';
              return val
            },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'stowageFactor', label: T(`Stowage Factor (CBM/MT)`), width: 120,
            fieldDataGetter(record: any) {
              let stowageFactor = record['stowageFactor'] || 0;
              let val = '';
              if (stowageFactor > 0) val += stowageFactor + ' (CBM / MT)';
              return val
            },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'loadRate', label: T(`Load Rate`), width: 120,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'dischargeRate', label: T(`Discharge Rate`), width: 120,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'stackable', label: T(`Stackable`), width: 120,
            fieldDataGetter(record: any) {
              let stackable = record['stackable'];
              let val = stackable == 'STACKABLE' ? 'Stackable' : 'Non Stackable';
              return val
            },
          },
          {
            name: 'stackableType', label: T(`Stackable Type`), width: 120,
            fieldDataGetter(record: any) {
              let stackableType = record['stackableType'];
              if (!stackableType) return '';
              let val = stackableType == 'ON_DECK' ? 'On deck' : 'Under deck';
              return val
            },
          },
          {
            name: 'allowCombine', label: T(`Allow Combine`), width: 120,
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              if (record['allowCombine']) {
                return (<div className='flex-vbox align-items-center'>
                  <FeatherIcon.CheckCircle size={14} className="text-success" />
                </div>)
              }
              return <></>
            }
          },
          {
            name: 'feedback', label: T(`Feedback`), width: 270, style: { height: 40 },
            editor: {
              type: 'string',
              enable: (writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  record['feedback'] = newVal;
                  record['remindDate'] = null;
                  const { fieldConfig } = ctx;
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);
                  this.saveChanges(record)
                }
              },
            }
          },
          {
            name: 'targetRate', label: T(`Target Rate`), width: 200,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'pricingNote', label: T(`Pricing Note`), width: 270, style: { height: 40 },
            editor: {
              type: 'string',
              enable: (writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  record['pricingNote'] = newVal;
                  record['remindDate'] = null;

                  record['processStatus'] = BulkCargoProcessStatus.OFFERED.value;
                  this.saveChanges(record)
                  const { fieldConfig } = ctx;
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);
                }
              },
            }
          },
          {
            name: 'validDate', label: T(`Valid Date`), width: 120,
            editor: {
              type: 'date',
              enable: (writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (!record['pricingNote'] || record['pricingNote'].trim() === '') {
                  bs.dialogShow('Warning',
                    <div className="text-warning fw-bold text-center py-3 border-bottom">
                      <FeatherIcon.AlertCircle className="mx-2" />
                      Please enter Pricing Note first!
                    </div>,
                    { backdrop: 'static', size: 'md' }
                  );
                  record['validDate'] = oldVal;
                  this.vgridContext.getVGrid().forceUpdateView();
                  return;
                } else {
                  if (newVal !== oldVal) {
                    record['validDate'] = newVal;
                    const { fieldConfig } = ctx;
                    let event: grid.VGridCellEvent = {
                      row: record.row,
                      field: fieldConfig,
                      event: 'Modified'
                    }
                    this.vgridContext.broadcastCellEvent(event);
                    this.saveChanges(record)
                  }
                }
              },
            }
          },
          {
            name: 'remindDate', label: T(`Remind Date`), width: 120, style: { height: 40 },
            editor: {
              type: 'date',
              enable: (writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  record['remindDate'] = newVal;
                  const { fieldConfig } = ctx;
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);
                  this.saveChanges(record)
                }
              },
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord } = ctx;
                let record = displayRecord.record;
                return (
                  <input.BBDateInputMask bean={record} field={'remindDate'} format='DD/MM/YYYY' timeFormat={true}
                    onInputChange={onInputChange} style={{ height: '40px' }} />
                )
              }
            },
          },
          {
            name: "pricingLabel", label: 'Pricing By.', width: 130, filterable: true,
            fieldDataGetter(record) {
              return record['pricingLabel'] || 'N/A';
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let fieldName: string = event.field.name;
                if (fieldName === 'pricingNote' || fieldName === 'feedback' || fieldName === 'status') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let employeeName: string = record['pricingLabel'] || 'N/A';
              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length >= 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }
              return (
                <div className="flex-hbox">{employeeName}</div>
              )
            },
          },
          {
            name: 'pricingDate', label: T(`Pricing Date`), width: 120,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'pricingNote' || fieldName === 'feedback' || fieldName === 'status') {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'mailSubject', label: T(`Subject`), width: 340, style: { height: 40 },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let valTruncate = util.text.formater.uiTruncate(dRec.record[field.name], 340, true);
              return <div className="flex-hbox">{valTruncate}</div>
            }
          },
          {
            name: 'salemanStarRating', label: T('Rating'), width: 80, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let request = dRecord.record;
              let currentStar = RequestUtils.getStarRatingInfo(request['salemanStarRating']);
              let StarIcon = currentStar.icon;
              let label = currentStar.label;
              let hexColor = currentStar.hexColor;

              return (
                <div
                  className="flex-hbox flex-center px-2 py-2 rounded-2 w-100 h-100"
                  style={{ backgroundColor: hexColor, color: '#212529' }}>
                  <StarIcon size={14} className="me-1" />
                  <span>{label}</span>
                </div>
              );
            }
          },
          {
            name: 'status', label: T('Status'), width: 120,
            filterable: true, container: 'fixed-right',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'pricingNote' || fieldName === 'feedback' || fieldName === 'status') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let currentStatus = BulkCargoInquiryStatusUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = BulkCargoInquiryStatusUtils.getStatus();
              const statusRemaining = statusList.filter(status =>
                status.value !== record['status'] &&
                status.value !== 'IN_PROGRESS'
              );

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100"
                  title={T('Status')} closeOnTrigger=".btn" >
                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => {
                              if (opt.value === 'REJECTED') {
                                this.onRejectStatus(record);
                              } else {
                                record['status'] = opt.value;

                                if (opt.value === 'NO_FIRM' || opt.value === 'PRICE_MISMATCH' || opt.value === 'NO_RESPONSE') {
                                  record['processStatus'] = 'DROPPED';
                                }
                                this.saveChanges(record)
                                let event: grid.VGridCellEvent = {
                                  row: record.row,
                                  field: _field,
                                  event: 'Modified'
                                }
                                this.vgridContext.broadcastCellEvent(event);
                              }
                            }}>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },
          {
            name: 'purpose', label: T('Purpose'), width: 90,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'createdTime', label: T('Created Time'), width: 120, format: util.text.formater.compactDate,
            customRender: renderTooltipAdvanced,
          }
        ],
      },
      toolbar: { hide: true },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }

    let fields: grid.FieldConfig[] = config.record.fields || [];

    const totalWidth = window.innerWidth || 1920;
    const totalControlWidth = config.record.control ? config.record.control.width : 0;
    let totalWidthRemaining = totalWidth - totalControlWidth - fields.reduce((sum, field) => {
      const isVisible =
        !field.state ||
        !field.state.hasOwnProperty('visible') ||
        field.state.visible === true;
      return isVisible ? sum + (field.width || 0) : sum;
    }, 0);

    if (totalWidthRemaining > 0) {
      fields[fields.length - 1].container ? fields[fields.length - 1].container = 'default' : null;
    }

    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    if (this.vgridContext && this.vgridContext.getVGrid()) {
      let dRecords: Array<grid.DisplayRecord> = this.vgridContext.model.getDisplayRecordList().getDisplayRecords();
      for (let rec of dRecords) {
        grid.initRecordState(rec.record, rec.row)
        grid.getRecordState(rec.record).selected = false;
      }
      grid.getRecordState(dRecord.record).markModified();
      grid.getRecordState(dRecord.record).selected = true;
      this.selectedDisplayRecord = dRecord;
      this.forceUpdate();
      // this.vgridContext.getVGrid().forceUpdateView();
    }
  }

  onCopy(dRecord: grid.DisplayRecord): any {
    let { pageContext } = this.props;
    let bulkCargo = dRecord.record;
    let newEntity: any = {
      ...bulkCargo,
      id: undefined,
      code: undefined,
      feedback: '',
      pricingNote: undefined,
      status: 'CHECKING',
      processStatus: 'CHECKING',
      requestDate: util.TimeUtil.toCompactDateTimeFormat(new Date()),
      pricingDate: util.TimeUtil.toCompactDateTimeFormat(new Date()),
      laydaysDate: util.TimeUtil.toCompactDateTimeFormat(new Date()),
      pricingAccountId: SESSION.getAccountId(),
      pricingLabel: SESSION.getAccountAcl().getFullName(),
      cancellingDate: undefined,
    }

    let observer = new entity.ComplexBeanObserver(newEntity);
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UICheckRequestEditor
          appContext={appCtx} pageContext={pageCtx} observer={observer}
          onPostCommit={(_entity: any) => {
            pageCtx.back();
            this.reloadData();
          }} />
      );
    };
    pageContext.createPopupPage("copy-bulk-cargo", "Bulk Cargo Input Inquiry", createAppPage, { size: 'flex-lg', backdrop: 'static' });
  }

  saveChanges = (modified: any) => {
    let { appContext } = this.props;
    if (!modified['pricingAccountId']) {
      modified['pricingAccountId'] = SESSION.getAccountId();
      modified['pricingLabel'] = SESSION.getAccountAcl().getFullName();
      modified['pricingDate'] = util.TimeUtil.javaCompactDateTimeFormat(new Date())
    }
    this.vgridContext.model.addOrUpdateByRecordId(modified);

    appContext.createHttpBackendCall('TransportPriceMiscService', 'saveBulkCargoInquiryRequestRecords', { records: [modified] })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
        this.reloadData();
      })
      .call();
  }

  onRejectStatus = (record: any) => {
    const { pageContext } = this.props;
    let rejectData: any = {
      reason: '',
    };

    const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      const onSendEmail = () => {
        if (!rejectData.reason || rejectData.reason.trim() === '') {
          bs.dialogShow('Message',
            <div className="text-danger text-center p-2">
              Please enter rejection reason to notify the sales team!
            </div>
          );
          return;
        }
        record['status'] = 'REJECTED';
        record['processStatus'] = 'DROPPED';
        record['rejectReason'] = rejectData.reason;
        this.saveChanges(record);
        pageCtx.back();
      };
      return (
        <div className='flex-vbox'>
          <div className='flex-vbox p-1'>
            <input.BBTextField bean={rejectData} field={"reason"} style={{ height: '8em' }}
              placeholder="Please enter rejection reason to notify the sales team..." />
          </div>
          <div className='flex-hbox-grow-0 justify-content-end mt-1 border-top pt-2'>
            <bs.Button laf='info' outline className="px-2 py-1 mx-1" onClick={onSendEmail} >
              <FeatherIcon.Send size={12} /> Send
            </bs.Button>
          </div>
        </div>
      );
    };

    pageContext.createPopupPage(`reject-reason-${util.IDTracker.next()}`, `Reject Inquiry #${record.code} `, createAppPage, { size: 'md', backdrop: 'static' });
  }

}

export class UIBulkCargoInquiryRequestTrackingListPage extends app.AppComponent {
  bulkCargoRef: RefObject<UIBulkCargoInquiryRequestTrackingList>;
  viewId: number = util.IDTracker.next();
  plugin: UIBulkCargoInquiryRequestReportPlugin;
  filter: any = { maxReturn: 500, enableJobStep: false, pattern: '' };

  constructor(props: app.AppComponentProps) {
    super(props);
    this.bulkCargoRef = React.createRef();
    this.plugin = new UIBulkCargoInquiryRequestReportPlugin('System');
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    this.forceUpdate();
  };

  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.filter.pattern = newVal;
      this.plugin = new UIBulkCargoInquiryRequestReportPlugin('System')
      if (newVal) {
        if (newVal.length > 3) this.plugin.withSearchPattern(`* ${newVal}* `)
      }
      this.viewId = util.IDTracker.next();
      this.forceUpdate();
    }
  }

  componentDidMount(): void {
    if (this.bulkCargoRef.current) this.forceUpdate();
  }

  onBulkInput = () => {
    const { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
        pageCtx.back();
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      }

      return (
        <UICheckRequestListEditor appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin([])}
          onPostCommit={onPostCommit} />
      )
    }
    let popupId = `bulk-input-${util.IDTracker.next()}`;
    pageContext.createPopupPage(popupId, "Bulk Cargo Request", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onAddBulkCargo = () => {
    const { pageContext } = this.props;

    let newEntity: any = {
      'requestDate': util.TimeUtil.toCompactDateTimeFormat(new Date()),
      'pricingDate': util.TimeUtil.toCompactDateTimeFormat(new Date()),
      'laydaysDate': util.TimeUtil.toCompactDateTimeFormat(new Date()),
      'pricingAccountId': SESSION.getAccountId(),
      'pricingLabel': SESSION.getAccountAcl().getFullName(),
      'status': 'IN_PROGRESS',
      'cargoType': 'BULK'
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UICheckRequestEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(newEntity)}
          onPostCommit={(_entity: any) => {
            pageCtx.back();
            this.viewId = util.IDTracker.next();
            if (this.bulkCargoRef.current) {
              this.bulkCargoRef.current.reloadData();
              this.bulkCargoRef.current.getVGridContext().getVGrid().forceUpdateView();
            }
            // this.forceUpdate();
          }} />
      )
    }
    let popupId = `bulk-cargo-${util.IDTracker.next()}`;
    pageContext.createPopupPage(popupId, "Bulk Cargo Input Inquiry", createAppPage, { size: 'flex-lg', backdrop: 'static' });
  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>
          <div className='flex-hbox'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={this.filter} field={"maxReturn"}
                options={[500, 1000, 2000, 5000]}
                optionLabels={['Show 500 records', 'Show 1000 records', 'Show 2000 records', 'Show 5000 records']}
                onInputChange={this.onModify} />
            </div>

            <input.WStringInput className={'flex-hbox'} style={{ maxWidth: 300 }}
              name='search' value={this.filter.pattern}
              placeholder={('Enter Ref or Subject...')} onChange={this.onChangePattern} />

          </div>

          <div className="flex-hbox flex-grow-0 justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onAddBulkCargo} hidden={bs.ScreenUtil.isMobileScreen()}>
              <FeatherIcon.Plus size={12} /> Bulk Cargo (Form)
            </bs.Button>

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onBulkInput} hidden={bs.ScreenUtil.isMobileScreen()}>
              <FeatherIcon.Plus size={12} /> Bulk Cargo (List)
            </bs.Button>

            {
              this.bulkCargoRef.current ?
                <XLSXButton appContext={appContext} pageContext={pageContext} context={this.bulkCargoRef.current.getVGridContext()} />
                : null
            }
          </div>
        </div>

        <div className='flex-vbox' key={this.viewId}>
          <UIBulkCargoInquiryRequestTrackingList ref={this.bulkCargoRef}
            appContext={appContext} pageContext={pageContext}
            plugin={this.plugin} />
        </div>
      </div>
    )
  }
}

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    model["fileName"] = 'Bulk Cargo.xlsx'
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
      for (let field of fieldGroup.fields) {
        field.dataType = field.dataType?.toLowerCase();
      }
    }

    for (let field of model.fields) {
      field.dataType = field.dataType?.toLowerCase();
    }
    return model;
  }

  render() {
    const { style, disable } = this.props;
    return (
      <bs.Button laf={"info"} onClick={this.onExportCustomization} className="border-0 border-end rounded-0 p-1 mx-1"
        style={style} outline disabled={disable} >
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}
